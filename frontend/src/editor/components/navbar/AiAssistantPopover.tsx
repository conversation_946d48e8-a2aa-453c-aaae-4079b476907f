import React, { useState, useRef, useEffect } from "react";
import {
  Box,
  TextField,
  IconButton,
  Typography,
  Avatar,
  Divider,
  Paper,
  Chip,
} from "@mui/material";
import {
  Send as SendIcon,
  SmartToy as AiIcon,
  Person as PersonIcon,
} from "@mui/icons-material";
import { useLanguage } from "../../../i18n/LanguageContext";
import { CustomPopover } from "../CustomPopover";

interface Message {
  id: string;
  content: string;
  sender: "user" | "ai";
  timestamp: Date;
}

export const AiAssistantPopover: React.FC = () => {
  const { t } = useLanguage();
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "1",
      content: t("ai_welcome_message"),
      sender: "ai",
      timestamp: new Date(),
    },
  ]);
  const [inputValue, setInputValue] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [windowSize, setWindowSize] = useState({
    width: window.innerWidth,
    height: window.innerHeight,
  });
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputValue.trim(),
      sender: "user",
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setInputValue("");
    setIsLoading(true);

    // 模拟AI回复（这里可以替换为实际的AI API调用）
    setTimeout(() => {
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: t("ai_sample_response"),
        sender: "ai",
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, aiMessage]);
      setIsLoading(false);
    }, 1000);
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault();
      handleSendMessage();
    }
  };

  const quickActions = [
    t("ai_quick_action_help"),
    t("ai_quick_action_suggestions"),
    t("ai_quick_action_export"),
  ];

  const buttonContent = (
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
        gap: 1,
        px: 1.5,
        py: 0.75,
        borderRadius: 2,
        background: (theme) =>
          theme.palette.mode === "dark"
            ? "linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
            : "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
        color: "white",
        "&:hover": {
          transform: "translateY(-1px)",
          boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
        },
        transition: "all 0.2s ease-in-out",
      }}
    >
      <AiIcon sx={{ fontSize: 18 }} />
      <Typography variant="body2" sx={{ fontWeight: 600, color: "white" }}>
        AI Agent
      </Typography>
    </Box>
  );

  // 计算响应式宽度和高度
  const getPopoverDimensions = () => {
    const { width: windowWidth, height: windowHeight } = windowSize;
    const maxWidth = Math.min(400, windowWidth - 40); // 至少留40px边距
    const minWidth = Math.min(380, windowWidth - 60);
    const maxHeight = Math.min(520, windowHeight - 120); // 为navbar和底部留空间

    return { minWidth, maxWidth, maxHeight };
  };

  const { minWidth, maxWidth, maxHeight } = getPopoverDimensions();

  return (
    <CustomPopover
      customTrigger={buttonContent}
      title={t("ai_assistant")}
      minWidth={minWidth}
      maxWidth={maxWidth}
      popoverProps={{
        anchorOrigin: {
          vertical: "bottom",
          horizontal: "right",
        },
        transformOrigin: {
          vertical: "top",
          horizontal: "right",
        },
        sx: {
          "& .MuiPaper-root": {
            borderRadius: 3,
            boxShadow: "0 8px 32px rgba(0,0,0,0.12)",
            border: "1px solid",
            borderColor: "divider",
            backdropFilter: "blur(8px)",
            background: (theme) =>
              theme.palette.mode === "dark"
                ? "rgba(30, 30, 30, 0.95)"
                : "rgba(255, 255, 255, 0.95)",
          },
        },
      }}
    >
      <Box
        sx={{
          width: "100%",
          height: maxHeight,
          display: "flex",
          flexDirection: "column",
          background: (theme) =>
            theme.palette.mode === "dark"
              ? "linear-gradient(180deg, rgba(30,30,30,0.05) 0%, rgba(30,30,30,0.02) 100%)"
              : "linear-gradient(180deg, rgba(255,255,255,0.05) 0%, rgba(255,255,255,0.02) 100%)",
          overflow: "hidden",
        }}
      >
        {/* 快捷操作 */}
        <Box sx={{ p: 2.5, pb: 1.5 }}>
          <Typography
            variant="body2"
            sx={{
              mb: 1.5,
              color: "text.secondary",
              fontWeight: 500,
              fontSize: "0.8rem",
              textTransform: "uppercase",
              letterSpacing: "0.5px",
            }}
          >
            {t("ai_quick_actions")}
          </Typography>
          <Box sx={{ display: "flex", gap: 1, flexWrap: "wrap" }}>
            {quickActions.map((action, index) => (
              <Chip
                key={index}
                label={
                  windowSize.width < 400
                    ? action.slice(0, 10) + (action.length > 10 ? "..." : "")
                    : action
                }
                size="small"
                variant="outlined"
                onClick={() => setInputValue(action)}
                sx={{
                  cursor: "pointer",
                  borderRadius: 2,
                  fontSize: windowSize.width < 400 ? "0.7rem" : "0.75rem",
                  minWidth: "auto",
                  "&:hover": {
                    backgroundColor: "primary.main",
                    color: "primary.contrastText",
                    borderColor: "primary.main",
                    transform: "translateY(-1px)",
                  },
                  transition: "all 0.2s ease-in-out",
                }}
              />
            ))}
          </Box>
        </Box>

        <Divider />

        {/* 消息列表 */}
        <Box
          sx={{
            flex: 1,
            overflowY: "auto",
            p: 2.5,
            pt: 1.5,
            display: "flex",
            flexDirection: "column",
            gap: 2,
            "&::-webkit-scrollbar": {
              width: "6px",
            },
            "&::-webkit-scrollbar-track": {
              background: "transparent",
            },
            "&::-webkit-scrollbar-thumb": {
              background: (theme) =>
                theme.palette.mode === "dark"
                  ? "rgba(255,255,255,0.2)"
                  : "rgba(0,0,0,0.2)",
              borderRadius: "3px",
            },
            "&::-webkit-scrollbar-thumb:hover": {
              background: (theme) =>
                theme.palette.mode === "dark"
                  ? "rgba(255,255,255,0.3)"
                  : "rgba(0,0,0,0.3)",
            },
          }}
        >
          {messages.map((message) => (
            <Box
              key={message.id}
              sx={{
                display: "flex",
                alignItems: "flex-start",
                gap: 1.5,
                flexDirection:
                  message.sender === "user" ? "row-reverse" : "row",
                animation: "fadeIn 0.3s ease-in-out",
                "@keyframes fadeIn": {
                  from: { opacity: 0, transform: "translateY(10px)" },
                  to: { opacity: 1, transform: "translateY(0)" },
                },
              }}
            >
              <Avatar
                sx={{
                  width: 28,
                  height: 28,
                  background:
                    message.sender === "user"
                      ? "linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%)"
                      : "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
                  boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
                }}
              >
                {message.sender === "user" ? (
                  <PersonIcon sx={{ fontSize: 16, color: "text.primary" }} />
                ) : (
                  <AiIcon sx={{ fontSize: 16, color: "text.primary" }} />
                )}
              </Avatar>
              <Paper
                elevation={0}
                sx={{
                  p: 2,
                  maxWidth: windowSize.width < 480 ? "85%" : "80%",
                  background:
                    message.sender === "user"
                      ? "linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
                      : (theme) =>
                          theme.palette.mode === "dark"
                            ? "rgba(50, 50, 50, 0.8)"
                            : "rgba(245, 245, 245, 0.9)",
                  color:
                    message.sender === "user" ? "text.primary" : "text.primary",
                  borderRadius:
                    message.sender === "user"
                      ? "18px 18px 4px 18px"
                      : "18px 18px 18px 4px",
                  border: "1px solid",
                  borderColor:
                    message.sender === "user" ? "transparent" : "divider",
                  backdropFilter: "blur(8px)",
                  boxShadow: "0 2px 12px rgba(0,0,0,0.1)",
                }}
              >
                <Typography
                  variant="body2"
                  sx={{
                    fontSize: "0.875rem",
                    lineHeight: 1.5,
                    color:
                      message.sender === "user"
                        ? "text.secondary"
                        : "text.secondary",
                  }}
                >
                  {message.content}
                </Typography>
              </Paper>
            </Box>
          ))}
          {isLoading && (
            <Box
              sx={{
                display: "flex",
                alignItems: "flex-start",
                gap: 1.5,
                animation: "fadeIn 0.3s ease-in-out",
              }}
            >
              <Avatar
                sx={{
                  width: 28,
                  height: 28,
                  background:
                    "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
                  boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
                }}
              >
                <AiIcon sx={{ fontSize: 16, color: "white" }} />
              </Avatar>
              <Paper
                elevation={0}
                sx={{
                  p: 2,
                  background: (theme) =>
                    theme.palette.mode === "dark"
                      ? "rgba(50, 50, 50, 0.8)"
                      : "rgba(245, 245, 245, 0.9)",
                  borderRadius: "18px 18px 18px 4px",
                  border: "1px solid",
                  borderColor: "divider",
                  backdropFilter: "blur(8px)",
                  boxShadow: "0 2px 12px rgba(0,0,0,0.1)",
                }}
              >
                <Typography
                  variant="body2"
                  sx={{
                    color: "text.secondary",
                    fontSize: "0.875rem",
                    display: "flex",
                    alignItems: "center",
                    gap: 1,
                  }}
                >
                  <Box
                    sx={{
                      display: "flex",
                      gap: 0.5,
                      "& > div": {
                        width: 4,
                        height: 4,
                        borderRadius: "50%",
                        backgroundColor: "primary.main",
                        animation: "bounce 1.5s infinite ease-in-out",
                      },
                      "& > div:nth-of-type(1)": { animationDelay: "0s" },
                      "& > div:nth-of-type(2)": { animationDelay: "0.1s" },
                      "& > div:nth-of-type(3)": { animationDelay: "0.2s" },
                      "@keyframes bounce": {
                        "0%, 80%, 100%": { transform: "scale(0)" },
                        "40%": { transform: "scale(1)" },
                      },
                    }}
                  ></Box>
                  {t("ai_typing")}...
                </Typography>
              </Paper>
            </Box>
          )}
          <div ref={messagesEndRef} />
        </Box>

        {/* 输入区域 */}
        <Box
          sx={{
            p: 2.5,
            pt: 2,
            borderTop: "1px solid",
            borderColor: "divider",
            display: "flex",
            gap: 1.5,
            alignItems: "flex-end",
            background: (theme) =>
              theme.palette.mode === "dark"
                ? "rgba(20, 20, 20, 0.6)"
                : "rgba(250, 250, 250, 0.8)",
            backdropFilter: "blur(8px)",
          }}
        >
          <TextField
            fullWidth
            multiline
            maxRows={4}
            placeholder={t("ai_input_placeholder")}
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            disabled={isLoading}
            variant="outlined"
            size="small"
            sx={{
              "& .MuiOutlinedInput-root": {
                borderRadius: 3,
                fontSize: "0.875rem",
                backgroundColor: (theme) =>
                  theme.palette.mode === "dark"
                    ? "rgba(40, 40, 40, 0.8)"
                    : "rgba(255, 255, 255, 0.9)",
                border: "1px solid",
                borderColor: "divider",
                backdropFilter: "blur(8px)",
                "&:hover": {
                  borderColor: "primary.main",
                },
                "&.Mui-focused": {
                  borderColor: "primary.main",
                  boxShadow: "0 0 0 2px rgba(25, 118, 210, 0.1)",
                },
              },
              "& .MuiOutlinedInput-input": {
                padding: "12px 14px",
              },
            }}
          />
          <IconButton
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isLoading}
            sx={{
              height: 40,
              width: 40,
              background:
                inputValue.trim() && !isLoading
                  ? "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)"
                  : "transparent",
              color:
                inputValue.trim() && !isLoading ? "white" : "text.secondary",
              border: "1px solid",
              borderColor:
                inputValue.trim() && !isLoading ? "transparent" : "divider",
              "&:hover": {
                background:
                  inputValue.trim() && !isLoading
                    ? "linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
                    : "action.hover",
                transform:
                  inputValue.trim() && !isLoading ? "translateY(-1px)" : "none",
                boxShadow:
                  inputValue.trim() && !isLoading
                    ? "0 4px 12px rgba(0,0,0,0.15)"
                    : "none",
              },
              "&:disabled": {
                background: "transparent",
                color: "text.disabled",
                borderColor: "divider",
              },
              transition: "all 0.2s ease-in-out",
            }}
          >
            <SendIcon sx={{ fontSize: 18 }} />
          </IconButton>
        </Box>
      </Box>
    </CustomPopover>
  );
};
