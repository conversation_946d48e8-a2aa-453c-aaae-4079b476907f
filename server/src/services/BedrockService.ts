import {
  BedrockRuntimeClient,
  InvokeModelCommand,
} from "@aws-sdk/client-bedrock-runtime";
import logger from "../utils/logger";
import {
  AiChatRequest,
  AiChatResponse,
  CanvasStateForAI,
  AiErrorCode,
} from "../types/ai";
import { AiError } from "../middleware/aiErrorHandler";

export class BedrockService {
  private client: BedrockRuntimeClient;
  private modelId: string = "us.anthropic.claude-3-5-sonnet-20241022-v2:0";

  constructor() {
    // 初始化Bedrock客户端
    this.client = new BedrockRuntimeClient({
      region: process.env.AWS_REGION || "us-west-2",
      // credentials: {
      //   accessKeyId: process.env.AWS_ACCESS_KEY_ID || "",
      //   secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || "",
      // },
    });
  }

  /**
   * 调用Claude Sonnet进行AI对话
   * @param request 包含用户输入和画布状态的请求
   * @returns AI响应
   */
  async chat(request: AiChatRequest): Promise<AiChatResponse> {
    try {
      const { userInput, canvasState } = request;

      // 构建系统提示词
      const systemPrompt = this.buildSystemPrompt(canvasState);

      // 构建用户消息
      const userMessage = this.buildUserMessage(userInput, canvasState);

      // 准备Claude API请求
      const payload = {
        anthropic_version: "bedrock-2023-05-31",
        max_tokens: 1000,
        system: systemPrompt,
        messages: [
          {
            role: "user",
            content: userMessage,
          },
        ],
      };

      logger.info("调用Bedrock Claude Sonnet...");

      const command = new InvokeModelCommand({
        modelId: this.modelId,
        body: JSON.stringify(payload),
        contentType: "application/json",
        accept: "application/json",
      });

      const response = await this.client.send(command);

      if (!response.body) {
        throw new Error("Bedrock响应为空");
      }

      // 解析响应
      const responseBody = JSON.parse(new TextDecoder().decode(response.body));
      const aiResponse = responseBody.content[0].text;

      logger.info("Bedrock调用成功");

      return {
        response: aiResponse,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      logger.error("Bedrock调用失败:", error);

      // 如果是AWS凭证问题，抛出特定错误
      if (error instanceof Error && error.message.includes("credentials")) {
        throw new AiError(
          "AWS凭证配置错误",
          AiErrorCode.AWS_CREDENTIALS_ERROR,
          500,
          "请检查AWS_ACCESS_KEY_ID、AWS_SECRET_ACCESS_KEY和AWS_REGION环境变量设置"
        );
      }

      // 如果是Bedrock API错误
      if (
        error instanceof Error &&
        (error.message.includes("Bedrock") || error.message.includes("bedrock"))
      ) {
        throw new AiError(
          "AI服务调用失败",
          AiErrorCode.BEDROCK_API_ERROR,
          500,
          error.message
        );
      }

      // 其他错误
      throw new AiError(
        "AI服务内部错误",
        AiErrorCode.INTERNAL_ERROR,
        500,
        error instanceof Error ? error.message : "未知错误"
      );
    }
  }

  /**
   * 构建系统提示词
   * @param canvasState 画布状态
   * @returns 系统提示词
   */
  private buildSystemPrompt(canvasState: CanvasStateForAI): string {
    return `你是一个专业的视频编辑AI助手，专门帮助用户使用Fabric视频编辑器。你的任务是：

1. 分析用户的视频项目状态
2. 提供专业的视频编辑建议
3. 回答关于视频制作的问题
4. 帮助优化视频内容和布局

当前项目信息：
'''
# 视频编辑器 Canvas State 完整结构说明

## 概述

Canvas State 是视频编辑器的核心数据结构，包含了一个视频项目的完整状态信息，包括画布设置、元素、动画、字幕和轨道等所有配置。

## 1. 主要 Canvas State 对象

```typescript
interface CanvasState {
  backgroundColor: string;           // 画布背景颜色 (如: "#111111")
  width: number;                    // 画布宽度 (默认: 1280)
  height: number;                   // 画布高度 (默认: 720)
  elements: EditorElement[];        // 编辑器元素数组
  animations: Animation[];          // 动画数组
  captions: Caption[];             // 字幕数组
  globalCaptionStyle: any;         // 全局字幕样式
  tracks: Track[];                 // 轨道数组
  outputFormat?: string;           // 输出格式 (默认: "mp4")
}
```

## 2. 编辑器元素 (EditorElement)

### 基础结构
```typescript
interface EditorElement {
  id: string;                      // 唯一标识符
  type: ElementType;               // 元素类型
  name: string;                    // 元素名称
  placement: Placement;            // 位置信息
  timeFrame: TimeFrame;            // 时间帧信息
  properties: ElementProperties;    // 元素属性
  trackId?: string;                // 所属轨道ID
  locked?: boolean;                // 是否锁定 (默认: false)
  opacity?: number;                // 透明度 (默认: 1)
  fabricObject?: fabric.Object;    // Fabric.js对象引用
  transition?: TransitionInfo;     // 过渡动画信息
}
```

### 元素类型 (ElementType)
- `"video"` - 视频元素
- `"image"` - 图片元素
- `"audio"` - 音频元素
- `"text"` - 文本元素
- `"shape"` - 形状元素

## 3. 位置信息 (Placement)

```typescript
interface Placement {
  x: number;                       // X坐标
  y: number;                       // Y坐标
  width: number;                   // 宽度
  height: number;                  // 高度
  rotation: number;                // 旋转角度
  scaleX: number;                  // X轴缩放
  scaleY: number;                  // Y轴缩放
}
```

## 4. 时间帧信息 (TimeFrame)

```typescript
interface TimeFrame {
  start: number;                   // 开始时间 (毫秒)
  end: number;                     // 结束时间 (毫秒)
}
```

## 5. 元素属性 (ElementProperties)

### 视频元素属性
```typescript
interface VideoProperties {
  src: string;                     // 视频源路径
  volume?: number;                 // 音量 (0-1)
  playbackRate?: number;           // 播放速率
  loop?: boolean;                  // 是否循环
  muted?: boolean;                 // 是否静音
}
```

### 图片元素属性
```typescript
interface ImageProperties {
  src: string;                     // 图片源路径
  filters?: any[];                 // 滤镜数组
}
```

### 音频元素属性
```typescript
interface AudioProperties {
  src: string;                     // 音频源路径
  volume?: number;                 // 音量 (0-1)
  loop?: boolean;                  // 是否循环
  fadeIn?: number;                 // 淡入时间
  fadeOut?: number;                // 淡出时间
}
```

### 文本元素属性
```typescript
interface TextProperties {
  text: string;                    // 文本内容
  fontSize: number;                // 字体大小
  fontFamily: string;              // 字体族
  fontColor: string;               // 字体颜色
  fontWeight?: string;             // 字体粗细
  textAlign?: string;              // 文本对齐
  backgroundColor?: string;         // 背景颜色
  borderStyle?: BorderStyle;       // 边框样式
  padding?: number;                // 内边距
}
```

### 形状元素属性
```typescript
interface ShapeProperties {
  shapeType: ShapeType;            // 形状类型
  fill?: string;                   // 填充颜色
  stroke?: string;                 // 边框颜色
  strokeWidth?: number;            // 边框宽度
  borderRadius?: number;           // 圆角半径
}
```

#### 形状类型 (ShapeType)
- `"rectangle"` - 矩形
- `"circle"` - 圆形
- `"triangle"` - 三角形
- `"line"` - 直线
- `"ellipse"` - 椭圆

## 6. 动画对象 (Animation)

```typescript
interface Animation {
  id: string;                      // 动画ID
  targetId: string;                // 目标元素ID
  type: AnimationType;             // 动画类型
  startTime: number;               // 开始时间
  duration: number;                // 持续时间
  properties: AnimationProperties; // 动画属性
  easing?: string;                 // 缓动函数
}
```

### 动画类型
- `fadeIn` / `fadeOut` - 淡入/淡出
- `breathe` - 呼吸效果
- `bounce` - 弹跳效果
- `shake` - 震动效果
- `flash` - 闪烁效果
- `rotate` - 旋转
- `zoom` / `zoomIn` / `zoomOut` - 缩放

## 7. 字幕对象 (Caption)

```typescript
interface Caption {
  id: string;                      // 字幕ID
  text: string;                    // 字幕文本
  startTime: number;               // 开始时间
  endTime: number;                 // 结束时间
  style?: CaptionStyle;            // 字幕样式
  position?: CaptionPosition;      // 字幕位置
}
```

## 8. 轨道对象 (Track)

```typescript
interface Track {
  id: string;                      // 轨道ID
  name: string;                    // 轨道名称
  type: TrackType;                 // 轨道类型
  elementIds: string[];            // 包含的元素ID数组
  isVisible: boolean;              // 是否可见
  isLocked: boolean;               // 是否锁定
}
```

### 轨道类型 (TrackType)
- `"media"` - 媒体轨道 (视频/图片)
- `"audio"` - 音频轨道
- `"text"` - 文本轨道
- `"caption"` - 字幕轨道

## 9. 过渡信息 (TransitionInfo)

```typescript
interface TransitionInfo {
  type: string;                    // 过渡类型
  duration?: number;               // 过渡持续时间
  direction?: string;              // 过渡方向
  easing?: string;                 // 缓动函数
}
```

### 过渡类型映射
- `fade` - 淡入淡出
- `circleopen` - 圆形展开
- `wiperight` / `wipeleft` / `wipedown` / `wipeup` - 擦除效果
- `slideright` / `slideleft` / `slidedown` / `slideup` - 滑动效果

## 10. 边框样式 (BorderStyle)

```typescript
interface BorderStyle {
  color?: string;                  // 边框颜色
  width?: number;                  // 边框宽度
  style?: string;                  // 边框样式 ("solid", "dashed", "dotted")
  radius?: number;                 // 圆角半径
}
```

## 11. 常用常量值

### 画布默认值
```typescript
DEFAULT_WIDTH: 1280              // 默认画布宽度
DEFAULT_HEIGHT: 720              // 默认画布高度
DEFAULT_BACKGROUND: "#111111"    // 默认背景颜色
```

### 元素默认持续时间
```typescript
TEXT_DURATION: 5000             // 文本元素默认持续时间 (5秒)
IMAGE_DURATION: 3000            // 图片元素默认持续时间 (3秒)
SHAPE_DURATION: 5000            // 形状元素默认持续时间 (5秒)
```

### 缩放范围
```typescript
MIN_SCALE: 0.1                  // 最小缩放比例
MAX_SCALE: 2.0                  // 最大缩放比例
DEFAULT_SCALE: 0.2              // 默认缩放比例
```

### 媒体元素默认尺寸
```typescript
DEFAULT_VIDEO_HEIGHT: 400       // 默认视频高度
DEFAULT_IMAGE_SIZE: 300         // 默认图片尺寸
DEFAULT_SHAPE_SIZE: 200         // 默认形状尺寸
DEFAULT_LINE_WIDTH: 300         // 默认线条宽度
DEFAULT_LINE_HEIGHT: 5          // 默认线条高度
DEFAULT_ELLIPSE_WIDTH: 250      // 默认椭圆宽度
DEFAULT_ELLIPSE_HEIGHT: 150     // 默认椭圆高度
```

## 12. 使用示例

### 完整的 Canvas State 示例
```json
{
  "backgroundColor": "#111111",
  "width": 1280,
  "height": 720,
  "elements": [
    {
      "id": "element-1",
      "type": "text",
      "name": "标题文本",
      "placement": {
        "x": 100,
        "y": 100,
        "width": 300,
        "height": 50,
        "rotation": 0,
        "scaleX": 1,
        "scaleY": 1
      },
      "timeFrame": {
        "start": 0,
        "end": 5000
      },
      "properties": {
        "text": "Hello World",
        "fontSize": 24,
        "fontFamily": "Arial",
        "fontColor": "#ffffff"
      },
      "trackId": "track-1",
      "locked": false,
      "opacity": 1
    }
  ],
  "animations": [],
  "captions": [],
  "tracks": [
    {
      "id": "track-1",
      "name": "Text Track",
      "type": "text",
      "elementIds": ["element-1"],
      "isVisible": true,
      "isLocked": false
    }
  ],
  "outputFormat": "mp4"
}
```

## 13. 注意事项

1. **时间单位**: 所有时间相关的值都以毫秒为单位
2. **坐标系统**: 使用标准的2D坐标系统，原点在左上角
3. **颜色格式**: 支持十六进制颜色格式 (如: "#ffffff")
4. **文件路径**: 媒体文件路径支持相对路径和绝对路径
5. **元素层级**: 元素在数组中的顺序决定了渲染层级
6. **轨道管理**: 每个元素都应该分配到相应的轨道中
7. **动画同步**: 动画的时间应该与元素的时间帧保持同步

这个结构提供了视频编辑器的完整状态描述，可以用于项目保存、加载、导出和AI分析等各种场景。
'''

请用中文回答，保持专业、友好和有帮助的语调。如果用户询问具体的编辑操作，请提供清晰的步骤说明。`;
  }

  /**
   * 构建用户消息
   * @param userInput 用户输入
   * @param canvasState 画布状态
   * @returns 用户消息
   */
  private buildUserMessage(
    userInput: string,
    canvasState: CanvasStateForAI
  ): string {
    // 分析画布元素
    console.log(canvasState);
    const elementAnalysis = this.analyzeElements(canvasState.elements || []);

    return `用户问题：${userInput}

当前项目结构详细信息：
${canvasState}
请基于以上信息回答用户的问题，并提供相关的编辑建议。`;
  }

  /**
   * 分析画布元素
   * @param elements 元素数组
   * @returns 元素分析结果
   */
  private analyzeElements(elements: any[]): string {
    if (elements.length === 0) {
      return "项目中暂无任何元素。";
    }

    const elementTypes = elements.reduce((acc, element) => {
      acc[element.type] = (acc[element.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const analysis = Object.entries(elementTypes)
      .map(([type, count]) => {
        const typeName = this.getElementTypeName(type);
        return `- ${typeName}：${count}个`;
      })
      .join("\n");

    return `元素分布：\n${analysis}`;
  }

  /**
   * 获取元素类型的中文名称
   * @param type 元素类型
   * @returns 中文名称
   */
  private getElementTypeName(type: string): string {
    const typeMap: Record<string, string> = {
      video: "视频",
      image: "图片",
      audio: "音频",
      text: "文本",
      shape: "形状",
    };
    return typeMap[type] || type;
  }

  /**
   * 检查AWS凭证是否配置
   * @returns 是否配置了凭证
   */
  isConfigured(): boolean {
    return true;
  }
}
